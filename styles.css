/* ===== CSS VARIABLES ===== */
:root {
  /* Colors */
  --primary-color: #8B5CF6;
  --secondary-color: #EC4899;
  --dark-bg: #1a1a1a;
  --darker-bg: #0f0f0f;
  --text-white: #ffffff;
  --text-gray: #a1a1aa;
  --text-light-gray: #d4d4d8;
  --card-bg: rgba(255, 255, 255, 0.05);
  --border-color: rgba(255, 255, 255, 0.1);
  
  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  --gradient-text: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  --gradient-glow: linear-gradient(135deg, rgba(139, 92, 246, 0.3), rgba(236, 72, 153, 0.3));
  
  /* Typography */
  --font-family: 'Inter', sans-serif;
  --h1-font-size: 3.5rem;
  --h2-font-size: 2rem;
  --h3-font-size: 1.5rem;
  --normal-font-size: 1rem;
  --small-font-size: 0.875rem;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* Spacing */
  --header-height: 4.5rem;
  --section-padding: 6rem 0;
  --container-margin: 0 1.5rem;
  --border-radius: 12px;
  --border-radius-small: 8px;
  
  /* Transitions */
  --transition-fast: 0.3s ease;
  --transition-medium: 0.5s ease;
  
  /* Z-index */
  --z-header: 100;
  --z-modal: 1000;
}

/* ===== BASE STYLES ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  font-size: var(--normal-font-size);
  font-weight: var(--font-weight-normal);
  background-color: var(--dark-bg);
  color: var(--text-white);
  line-height: 1.6;
  overflow-x: hidden;
}

/* ===== UTILITY CLASSES ===== */
.container {
  max-width: 1200px;
  margin: var(--container-margin);
  margin-left: auto;
  margin-right: auto;
}

.grid {
  display: grid;
  gap: 2rem;
}

.section {
  padding: var(--section-padding);
}

.gradient-text {
  background: var(--gradient-text);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* ===== BUTTONS ===== */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.875rem 1.5rem;
  border: none;
  border-radius: var(--border-radius-small);
  font-family: var(--font-family);
  font-size: var(--normal-font-size);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.btn--gradient {
  background: var(--gradient-primary);
  color: var(--text-white);
  box-shadow: 0 4px 20px rgba(139, 92, 246, 0.3);
}

.btn--gradient:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(139, 92, 246, 0.4);
}

.btn--gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: var(--transition-medium);
}

.btn--gradient:hover::before {
  left: 100%;
}

/* ===== HEADER ===== */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: rgba(26, 26, 26, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);
  z-index: var(--z-header);
  transition: var(--transition-fast);
}

.header.scroll-header {
  background: rgba(26, 26, 26, 0.98);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
}

.nav {
  height: var(--header-height);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav__logo .logo-circle {
  width: 40px;
  height: 40px;
  background: var(--gradient-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-white);
  font-size: 1.2rem;
}

.nav__list {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav__link {
  color: var(--text-light-gray);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: var(--transition-fast);
  position: relative;
}

.nav__link:hover,
.nav__link.active-link {
  color: var(--text-white);
}

.nav__link::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--gradient-primary);
  transition: var(--transition-fast);
}

.nav__link:hover::after,
.nav__link.active-link::after {
  width: 100%;
}

.nav__actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.nav__toggle,
.nav__close {
  display: none;
  font-size: 1.5rem;
  color: var(--text-white);
  cursor: pointer;
}

/* ===== HERO SECTION ===== */
.hero {
  padding-top: calc(var(--header-height) + 2rem);
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.hero__container {
  grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 4rem;
}

.hero__title {
  font-size: var(--h1-font-size);
  font-weight: var(--font-weight-bold);
  line-height: 1.2;
  margin-bottom: 1rem;
}

.hero__subtitle {
  font-size: var(--h2-font-size);
  font-weight: var(--font-weight-medium);
  color: var(--text-light-gray);
  margin-bottom: 1.5rem;
}

.hero__description {
  color: var(--text-gray);
  line-height: 1.7;
  margin-bottom: 2rem;
  max-width: 500px;
}

.hero__actions {
  margin-bottom: 2rem;
}

.hero__social {
  display: flex;
  gap: 1rem;
}

.social-link {
  width: 50px;
  height: 50px;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-small);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-light-gray);
  text-decoration: none;
  transition: var(--transition-fast);
  backdrop-filter: blur(10px);
}

.social-link:hover {
  background: var(--gradient-primary);
  color: var(--text-white);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.3);
}

.hero__image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero__img-wrapper {
  position: relative;
  width: 400px;
  height: 500px;
}

.hero__img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: var(--border-radius);
  position: relative;
  z-index: 2;
}

.hero__img-glow {
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  bottom: 20px;
  background: var(--gradient-glow);
  border-radius: var(--border-radius);
  filter: blur(30px);
  z-index: 1;
  opacity: 0.7;
}

/* ===== STATISTICS SECTION ===== */
.stats {
  padding: 3rem 0;
}

.stats__grid {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.stats__card {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 2rem;
  display: flex;
  align-items: center;
  gap: 1.5rem;
  transition: var(--transition-fast);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.stats__card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gradient-primary);
  transform: scaleX(0);
  transition: var(--transition-fast);
}

.stats__card:hover::before {
  transform: scaleX(1);
}

.stats__card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 40px rgba(139, 92, 246, 0.2);
}

.stats__icon {
  width: 60px;
  height: 60px;
  background: var(--gradient-primary);
  border-radius: var(--border-radius-small);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--text-white);
  flex-shrink: 0;
}

.stats__title {
  font-size: var(--h3-font-size);
  font-weight: var(--font-weight-bold);
  margin-bottom: 0.25rem;
}

.stats__subtitle {
  color: var(--text-gray);
  font-size: var(--small-font-size);
}

/* ===== RESPONSIVE DESIGN ===== */

/* Large devices (desktops, 1024px and up) */
@media screen and (min-width: 1024px) {
  .container {
    margin-left: auto;
    margin-right: auto;
  }

  .hero__container {
    gap: 6rem;
  }

  .hero__img-wrapper {
    width: 450px;
    height: 550px;
  }
}

/* Medium devices (tablets, 768px and up) */
@media screen and (max-width: 1023px) {
  :root {
    --h1-font-size: 2.5rem;
    --h2-font-size: 1.5rem;
    --section-padding: 4rem 0;
  }

  .hero__container {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 3rem;
  }

  .hero__content {
    order: 2;
  }

  .hero__image {
    order: 1;
  }

  .hero__img-wrapper {
    width: 300px;
    height: 400px;
  }

  .stats__grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Small devices (mobile, 767px and down) */
@media screen and (max-width: 767px) {
  :root {
    --h1-font-size: 2rem;
    --h2-font-size: 1.25rem;
    --header-height: 3.5rem;
    --section-padding: 3rem 0;
    --container-margin: 0 1rem;
  }

  .nav__menu {
    position: fixed;
    top: 0;
    right: -100%;
    width: 100%;
    height: 100vh;
    background: rgba(26, 26, 26, 0.98);
    backdrop-filter: blur(20px);
    padding: 6rem 2rem 2rem;
    transition: var(--transition-medium);
  }

  .nav__menu.show-menu {
    right: 0;
  }

  .nav__list {
    flex-direction: column;
    gap: 2.5rem;
    text-align: center;
  }

  .nav__link {
    font-size: 1.2rem;
  }

  .nav__close,
  .nav__toggle {
    display: block;
  }

  .nav__close {
    position: absolute;
    top: 1rem;
    right: 1rem;
  }

  .nav__actions .btn {
    display: none;
  }

  .hero {
    padding-top: calc(var(--header-height) + 1rem);
  }

  .hero__container {
    gap: 2rem;
  }

  .hero__img-wrapper {
    width: 250px;
    height: 320px;
  }

  .hero__description {
    max-width: 100%;
  }

  .stats__grid {
    grid-template-columns: 1fr;
  }

  .stats__card {
    padding: 1.5rem;
  }

  .btn {
    padding: 0.75rem 1.25rem;
    font-size: 0.9rem;
  }
}

/* Extra small devices (320px and down) */
@media screen and (max-width: 320px) {
  :root {
    --h1-font-size: 1.75rem;
    --container-margin: 0 0.75rem;
  }

  .hero__img-wrapper {
    width: 200px;
    height: 280px;
  }

  .stats__card {
    padding: 1rem;
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.hero__content {
  animation: fadeInLeft 1s ease-out;
}

.hero__image {
  animation: fadeInRight 1s ease-out 0.2s both;
}

.stats__card {
  animation: fadeInUp 0.8s ease-out;
}

.stats__card:nth-child(1) { animation-delay: 0.1s; }
.stats__card:nth-child(2) { animation-delay: 0.2s; }
.stats__card:nth-child(3) { animation-delay: 0.3s; }
.stats__card:nth-child(4) { animation-delay: 0.4s; }

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus styles for keyboard navigation */
.btn:focus,
.nav__link:focus,
.social-link:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --text-gray: #ffffff;
    --border-color: rgba(255, 255, 255, 0.3);
  }
}
