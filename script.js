/*===== MOBILE MENU TOGGLE =====*/
const navMenu = document.getElementById('nav-menu');
const navToggle = document.getElementById('nav-toggle');
const navClose = document.getElementById('nav-close');

// Show menu
if (navToggle) {
    navToggle.addEventListener('click', () => {
        navMenu.classList.add('show-menu');
        document.body.style.overflow = 'hidden'; // Prevent scrolling when menu is open
    });
}

// Hide menu
if (navClose) {
    navClose.addEventListener('click', () => {
        navMenu.classList.remove('show-menu');
        document.body.style.overflow = 'auto'; // Restore scrolling
    });
}

// Close menu when clicking on nav links
const navLinks = document.querySelectorAll('.nav__link');
navLinks.forEach(link => {
    link.addEventListener('click', () => {
        navMenu.classList.remove('show-menu');
        document.body.style.overflow = 'auto';
    });
});

/*===== HEADER SCROLL EFFECT =====*/
function scrollHeader() {
    const header = document.getElementById('header');
    // Add scroll effect when scroll is greater than 50 viewport height
    if (this.scrollY >= 50) {
        header.classList.add('scroll-header');
    } else {
        header.classList.remove('scroll-header');
    }
}
window.addEventListener('scroll', scrollHeader);

/*===== ACTIVE LINK HIGHLIGHTING =====*/
const sections = document.querySelectorAll('section[id]');

function scrollActive() {
    const scrollY = window.pageYOffset;

    sections.forEach(current => {
        const sectionHeight = current.offsetHeight;
        const sectionTop = current.offsetTop - 50;
        const sectionId = current.getAttribute('id');
        const navLink = document.querySelector('.nav__menu a[href*=' + sectionId + ']');

        if (scrollY > sectionTop && scrollY <= sectionTop + sectionHeight) {
            navLink?.classList.add('active-link');
        } else {
            navLink?.classList.remove('active-link');
        }
    });
}
window.addEventListener('scroll', scrollActive);

/*===== SMOOTH SCROLLING =====*/
const scrollLinks = document.querySelectorAll('a[href^="#"]');

scrollLinks.forEach(link => {
    link.addEventListener('click', (e) => {
        e.preventDefault();

        const targetId = link.getAttribute('href');
        const targetSection = document.querySelector(targetId);

        if (targetSection) {
            const headerHeight = document.querySelector('.header').offsetHeight;
            const targetPosition = targetSection.offsetTop - headerHeight;

            window.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            });
        }
    });
});

/*===== INTERSECTION OBSERVER FOR ANIMATIONS =====*/
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.animationPlayState = 'running';
        }
    });
}, observerOptions);

// Observe animated elements
const animatedElements = document.querySelectorAll('.hero__content, .hero__image, .stats__card');
animatedElements.forEach(el => {
    el.style.animationPlayState = 'paused';
    observer.observe(el);
});

/*===== BUTTON INTERACTIONS =====*/
const bookCallBtn = document.getElementById('book-call-btn');
const downloadCvBtns = document.querySelectorAll('.btn');

// Book a Call button functionality
if (bookCallBtn) {
    bookCallBtn.addEventListener('click', () => {
        // Placeholder for booking functionality
        alert('Booking functionality would be integrated here. This could connect to Calendly, Acuity Scheduling, or a custom booking system.');
    });
}

// Download CV button functionality
downloadCvBtns.forEach(btn => {
    if (btn.textContent.includes('Download CV')) {
        btn.addEventListener('click', () => {
            // Placeholder for CV download
            alert('CV download functionality would be implemented here. This would typically download a PDF file.');
        });
    }
});

/*===== SOCIAL LINKS =====*/
const socialLinks = document.querySelectorAll('.social-link');

socialLinks.forEach(link => {
    link.addEventListener('click', (e) => {
        e.preventDefault();

        const platform = link.querySelector('i').classList[1]; // Get the platform from icon class
        let url = '#';

        // Set placeholder URLs for social platforms
        switch(platform) {
            case 'fa-linkedin-in':
                url = 'https://linkedin.com/in/placeholder-profile';
                break;
            case 'fa-behance':
                url = 'https://behance.net/placeholder-profile';
                break;
            case 'fa-dribbble':
                url = 'https://dribbble.com/placeholder-profile';
                break;
        }

        // Open in new tab
        window.open(url, '_blank');
    });
});

/*===== PERFORMANCE OPTIMIZATIONS =====*/
// Lazy loading for images
const images = document.querySelectorAll('img');
const imageObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            const img = entry.target;
            img.src = img.src; // Trigger loading
            img.classList.add('loaded');
            imageObserver.unobserve(img);
        }
    });
});

images.forEach(img => imageObserver.observe(img));

/*===== KEYBOARD NAVIGATION =====*/
document.addEventListener('keydown', (e) => {
    // Close mobile menu with Escape key
    if (e.key === 'Escape' && navMenu.classList.contains('show-menu')) {
        navMenu.classList.remove('show-menu');
        document.body.style.overflow = 'auto';
    }
});

/*===== SCROLL TO TOP FUNCTIONALITY =====*/
let scrollTopBtn;

function createScrollTopButton() {
    scrollTopBtn = document.createElement('button');
    scrollTopBtn.innerHTML = '<i class="fas fa-arrow-up"></i>';
    scrollTopBtn.className = 'scroll-top';
    scrollTopBtn.setAttribute('aria-label', 'Scroll to top');

    // Add styles
    Object.assign(scrollTopBtn.style, {
        position: 'fixed',
        bottom: '2rem',
        right: '2rem',
        width: '50px',
        height: '50px',
        background: 'linear-gradient(135deg, #8B5CF6, #EC4899)',
        border: 'none',
        borderRadius: '50%',
        color: 'white',
        fontSize: '1.2rem',
        cursor: 'pointer',
        opacity: '0',
        visibility: 'hidden',
        transition: 'all 0.3s ease',
        zIndex: '99',
        boxShadow: '0 4px 20px rgba(139, 92, 246, 0.3)'
    });

    document.body.appendChild(scrollTopBtn);

    scrollTopBtn.addEventListener('click', () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

// Show/hide scroll to top button
function toggleScrollTopButton() {
    if (!scrollTopBtn) createScrollTopButton();

    if (window.scrollY > 500) {
        scrollTopBtn.style.opacity = '1';
        scrollTopBtn.style.visibility = 'visible';
    } else {
        scrollTopBtn.style.opacity = '0';
        scrollTopBtn.style.visibility = 'hidden';
    }
}

window.addEventListener('scroll', toggleScrollTopButton);

/*===== INITIALIZE =====*/
document.addEventListener('DOMContentLoaded', () => {
    // Add loaded class to body for CSS animations
    document.body.classList.add('loaded');

    // Initialize any additional functionality here
    console.log('Personal Landing Page Loaded Successfully! 🚀');
});