# Deployment Guide

## Quick Deployment Options

### 1. Netlify (Easiest - Drag & Drop)

1. **Build the project:**
   ```bash
   npm run build
   ```

2. **Deploy:**
   - Go to [netlify.com](https://netlify.com)
   - Drag and drop the `dist` folder
   - Your site is live instantly!

### 2. Vercel (Git Integration)

1. **Install Vercel CLI:**
   ```bash
   npm i -g vercel
   ```

2. **Deploy:**
   ```bash
   vercel
   ```

3. **Follow prompts** and your site will be live

### 3. GitHub Pages

1. **Build:**
   ```bash
   npm run build
   ```

2. **Create gh-pages branch:**
   ```bash
   git checkout -b gh-pages
   cp -r dist/* .
   git add .
   git commit -m "Deploy to GitHub Pages"
   git push origin gh-pages
   ```

3. **Enable GitHub Pages** in repository settings

## Custom Domain Setup

### For Netlify:
1. Go to Site Settings → Domain Management
2. Add your custom domain
3. Update DNS records as instructed

### For Vercel:
1. Go to Project Settings → Domains
2. Add your domain
3. Configure DNS records

## Environment Variables

If you add environment variables later:

**Netlify:**
- Site Settings → Environment Variables

**Vercel:**
- Project Settings → Environment Variables

## Performance Optimization

Before deploying:

1. **Optimize images:**
   - Use WebP format when possible
   - Compress images (TinyPNG, ImageOptim)
   - Use appropriate sizes

2. **Check bundle size:**
   ```bash
   npm run build
   # Check dist folder size
   ```

3. **Test performance:**
   - Use Lighthouse in Chrome DevTools
   - Aim for 90+ scores

## SEO Checklist

- [ ] Update meta description in `index.html`
- [ ] Add Open Graph tags
- [ ] Create `sitemap.xml`
- [ ] Add `robots.txt`
- [ ] Set up Google Analytics (optional)

## Post-Deployment Testing

1. **Test on multiple devices:**
   - Mobile phones
   - Tablets
   - Desktop browsers

2. **Check functionality:**
   - Navigation menu
   - Button interactions
   - Responsive design
   - Loading speed

3. **Validate:**
   - HTML validation
   - CSS validation
   - Accessibility check

## Monitoring

Consider adding:
- Google Analytics
- Hotjar for user behavior
- Uptime monitoring

## Backup

Always keep:
- Source code in Git repository
- Regular backups of any custom content
- Documentation of customizations
