# Personal Landing Page - UI/UX Designer Portfolio

A modern, responsive personal landing page designed for UI/UX designers. Features a dark theme with purple-pink gradients, smooth animations, and mobile-first responsive design.

## 🌟 Features

- **Responsive Design**: Mobile-first approach with breakpoints at 768px and 1024px
- **Modern UI**: Dark theme with purple-pink gradient accents
- **Smooth Animations**: CSS animations with intersection observer for performance
- **Accessibility**: WCAG compliant with keyboard navigation and screen reader support
- **Performance Optimized**: Lazy loading, efficient CSS, and optimized images
- **Interactive Elements**: Hover effects, smooth scrolling, and mobile menu

## 🎨 Design Highlights

- **Color Scheme**: Primary gradient from purple (#8B5CF6) to pink (#EC4899)
- **Typography**: Inter font family for modern, clean readability
- **Visual Effects**: Subtle glows, rounded corners, and backdrop blur effects
- **Professional Layout**: Hero section with statistics showcase

## 🚀 Quick Start

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn package manager

### Installation

1. **Clone or download the project**
   ```bash
   git clone <repository-url>
   cd personal-landing-page
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Open in browser**
   - Navigate to `http://localhost:5173`
   - The page will automatically reload when you make changes

### Build for Production

```bash
npm run build
```

This creates an optimized build in the `dist` folder ready for deployment.

## 📁 Project Structure

```
personal-landing-page/
├── index.html          # Main HTML structure
├── styles.css          # Complete CSS with responsive design
├── script.js           # JavaScript for interactivity
├── package.json        # Project dependencies and scripts
├── README.md           # This file
└── assets/             # Images and other assets (if added)
```

## 🎯 Customization Guide

### 1. Personal Information

**Update the following in `index.html`:**

- Replace "Placeholder Name" with your actual name
- Update the hero description with your experience
- Modify statistics in the stats section
- Add your actual social media links

### 2. Images

**Hero Image:**
- Replace the Unsplash URL in the hero section with your professional photo
- Recommended size: 500x600px for optimal display
- Use high-quality images with good lighting

**Logo:**
- Update the logo circle icon in the header
- Can be replaced with your actual logo or initials

### 3. Colors and Branding

**In `styles.css`, update CSS variables:**

```css
:root {
  --primary-color: #8B5CF6;    /* Your primary brand color */
  --secondary-color: #EC4899;   /* Your secondary brand color */
  --dark-bg: #1a1a1a;         /* Background color */
}
```

### 4. Content Sections

**Add more sections by:**
1. Adding new HTML sections in `index.html`
2. Creating corresponding CSS styles
3. Updating navigation menu
4. Adding smooth scroll functionality

## 📱 Responsive Breakpoints

- **Mobile**: 320px - 767px
- **Tablet**: 768px - 1023px  
- **Desktop**: 1024px and above

## 🌐 Deployment Options

### Option 1: Netlify (Recommended)

1. Build the project: `npm run build`
2. Drag and drop the `dist` folder to [Netlify](https://netlify.com)
3. Your site will be live instantly with a custom URL

### Option 2: Vercel

1. Install Vercel CLI: `npm i -g vercel`
2. Run: `vercel`
3. Follow the prompts to deploy

### Option 3: GitHub Pages

1. Build the project: `npm run build`
2. Push the `dist` folder contents to a `gh-pages` branch
3. Enable GitHub Pages in repository settings

### Option 4: Traditional Web Hosting

1. Build the project: `npm run build`
2. Upload the contents of the `dist` folder to your web server
3. Ensure your server serves `index.html` for all routes

## 🔧 Development Scripts

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm run preview` - Preview production build locally
- `npm run serve` - Serve production build on port 3000

## ♿ Accessibility Features

- Semantic HTML structure
- ARIA labels for interactive elements
- Keyboard navigation support
- High contrast mode support
- Reduced motion preferences respected
- Screen reader friendly

## 🎨 Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers (iOS Safari, Chrome Mobile)

## 📊 Performance Features

- **Lazy Loading**: Images load only when needed
- **CSS Optimization**: Efficient selectors and minimal reflows
- **JavaScript**: Vanilla JS for minimal bundle size
- **Animations**: Hardware-accelerated CSS animations
- **Fonts**: Optimized Google Fonts loading

## 🔄 Future Enhancements

Consider adding these features:

- **Contact Form**: Integration with Formspree or Netlify Forms
- **Blog Section**: For sharing design insights
- **Portfolio Gallery**: Showcase of your work
- **Testimonials**: Client feedback section
- **Dark/Light Mode Toggle**: Theme switching capability
- **Multi-language Support**: Internationalization

## 🐛 Troubleshooting

### Common Issues

**Development server won't start:**
- Ensure Node.js is installed (v16+)
- Delete `node_modules` and run `npm install` again
- Check if port 5173 is available

**Images not loading:**
- Verify image URLs are accessible
- Check network connectivity
- Ensure images are optimized for web

**Mobile menu not working:**
- Check JavaScript console for errors
- Ensure all script files are loaded
- Verify touch events are supported

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Feel free to submit issues and enhancement requests!

## 📞 Support

For questions or support, please open an issue in the repository or contact [<EMAIL>].

---

**Built with ❤️ using modern web technologies**
